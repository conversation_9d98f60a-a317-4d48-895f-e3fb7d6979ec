#!/usr/bin/env python3
import json
import requests
import time
import os

def test_text_only_chat():
    """测试纯文本对话"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        url = "http://localhost:9380/conversation/chat_with_vision"
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 模拟真实用户问题
        test_questions = [
            "你好，请介绍一下自己",
            "你能帮我做什么？",
            "请详细说明一下你的功能和能力",
            "你支持哪些语言？",
            "你能处理图片吗？"
        ]
        
        conversation_id = None
        
        for i, question in enumerate(test_questions):
            print(f"\n=== 测试问题 {i+1}: {question} ===")
            
            data = {
                "question": question,
                "stream": "false"
            }
            
            if conversation_id:
                data["conversation_id"] = conversation_id
            
            start_time = time.time()
            response = requests.post(url, headers=headers, data=data, timeout=60)
            end_time = time.time()
            
            print(f"响应时间: {end_time - start_time:.2f} 秒")
            print(f"响应状态码: {response.status_code}")
            print(f"响应大小: {len(response.text)} 字符")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0 and result.get('data'):
                        data_content = result['data']
                        answer = data_content.get('answer', '')
                        conversation_id = data_content.get('conversation_id', conversation_id)
                        
                        print(f"AI回答长度: {len(answer)} 字符")
                        print(f"对话ID: {conversation_id}")
                        
                        # 检查回答质量
                        if len(answer) > 5000:
                            print("⚠️ 回答过长，可能有问题")
                            # 检查重复内容
                            lines = answer.split('\n')
                            if len(lines) > 100:
                                print(f"  行数: {len(lines)}")
                                line_counts = {}
                                for line in lines:
                                    line = line.strip()
                                    if line:
                                        line_counts[line] = line_counts.get(line, 0) + 1
                                
                                if line_counts:
                                    max_repeat = max(line_counts.values())
                                    if max_repeat > 10:
                                        most_repeated = [line for line, count in line_counts.items() if count == max_repeat]
                                        print(f"  🔄 发现重复行: '{most_repeated[0][:50]}...' 重复 {max_repeat} 次")
                        elif len(answer) < 10:
                            print("⚠️ 回答过短")
                        else:
                            print("✅ 回答长度正常")
                        
                        # 显示回答内容（限制显示长度）
                        if len(answer) > 500:
                            print(f"回答内容: {answer[:250]}...{answer[-250:]}")
                        else:
                            print(f"回答内容: {answer}")
                    else:
                        print(f"API调用失败: {result}")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    break
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                break
                
            time.sleep(1)  # 避免请求过快
        
        return conversation_id
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_image_chat():
    """测试图片对话"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        url = "http://localhost:9380/conversation/chat_with_vision"
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 创建一个简单的测试图片
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # 创建测试图片
        img = Image.new('RGB', (400, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加一些文本和图形
        draw.rectangle([50, 50, 350, 250], outline='blue', width=3)
        draw.text((100, 100), "测试图片", fill='black')
        draw.text((100, 130), "Test Image", fill='red')
        draw.text((100, 160), "这是一个用于测试的图片", fill='green')
        draw.circle((200, 200), 30, outline='purple', width=2)
        
        # 保存为字节流
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 模拟真实用户的图片问题
        test_questions = [
            "请分析这张图片，告诉我你看到了什么",
            "这张图片里有什么文字？",
            "请描述一下图片的内容和布局",
            "图片中有哪些颜色？",
            "你能识别出图片中的形状吗？"
        ]
        
        conversation_id = None
        
        for i, question in enumerate(test_questions):
            print(f"\n=== 图片测试问题 {i+1}: {question} ===")
            
            # 准备文件数据
            files = {
                'image': ('test_image.png', img_buffer.getvalue(), 'image/png')
            }
            
            data = {
                "question": question,
                "stream": "false"
            }
            
            if conversation_id:
                data["conversation_id"] = conversation_id
            
            start_time = time.time()
            response = requests.post(url, headers=headers, data=data, files=files, timeout=120)
            end_time = time.time()
            
            print(f"响应时间: {end_time - start_time:.2f} 秒")
            print(f"响应状态码: {response.status_code}")
            print(f"响应大小: {len(response.text)} 字符")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0 and result.get('data'):
                        data_content = result['data']
                        answer = data_content.get('answer', '')
                        conversation_id = data_content.get('conversation_id', conversation_id)
                        has_image = data_content.get('has_image', False)
                        
                        print(f"AI回答长度: {len(answer)} 字符")
                        print(f"对话ID: {conversation_id}")
                        print(f"包含图片: {has_image}")
                        
                        # 检查回答质量
                        if len(answer) > 10000:
                            print("⚠️ 回答过长，可能有问题")
                            # 检查重复内容
                            lines = answer.split('\n')
                            if len(lines) > 200:
                                print(f"  行数: {len(lines)}")
                                line_counts = {}
                                for line in lines:
                                    line = line.strip()
                                    if line:
                                        line_counts[line] = line_counts.get(line, 0) + 1
                                
                                if line_counts:
                                    max_repeat = max(line_counts.values())
                                    if max_repeat > 10:
                                        most_repeated = [line for line, count in line_counts.items() if count == max_repeat]
                                        print(f"  🔄 发现重复行: '{most_repeated[0][:50]}...' 重复 {max_repeat} 次")
                                        print("  ❌ 检测到循环重复问题")
                                    else:
                                        print("  ✅ 没有明显重复")
                        elif len(answer) < 20:
                            print("⚠️ 回答过短")
                        else:
                            print("✅ 回答长度正常")
                        
                        # 显示回答内容（限制显示长度）
                        if len(answer) > 800:
                            print(f"回答内容: {answer[:400]}...{answer[-400:]}")
                        else:
                            print(f"回答内容: {answer}")
                            
                        # 检查图片信息
                        if 'image_info' in data_content:
                            image_info = data_content['image_info']
                            print(f"图片信息: {image_info}")
                            
                    else:
                        print(f"API调用失败: {result}")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    break
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                break
                
            time.sleep(2)  # 图片处理需要更多时间，间隔长一点
        
        return conversation_id
        
    except Exception as e:
        print(f"图片测试出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_mixed_conversation():
    """测试混合对话（文本+图片）"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        url = "http://localhost:9380/conversation/chat_with_vision"
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        print(f"\n=== 混合对话测试 ===")
        
        # 先进行文本对话
        data = {
            "question": "你好，我想测试一下你的图片理解能力",
            "stream": "false"
        }
        
        response = requests.post(url, headers=headers, data=data, timeout=60)
        conversation_id = None
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0 and result.get('data'):
                conversation_id = result['data'].get('conversation_id')
                print(f"文本对话创建成功，对话ID: {conversation_id}")
                print(f"AI回答: {result['data'].get('answer', '')}")
        
        if not conversation_id:
            print("创建对话失败")
            return None
        
        # 然后发送图片
        from PIL import Image, ImageDraw
        import io
        
        # 创建一个更复杂的测试图片
        img = Image.new('RGB', (600, 400), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # 绘制一些图形
        draw.rectangle([100, 100, 500, 300], fill='yellow', outline='red', width=3)
        draw.text((150, 150), "Hello World!", fill='black')
        draw.text((150, 180), "这是中文测试", fill='blue')
        draw.ellipse([200, 220, 300, 280], fill='green', outline='purple', width=2)
        draw.polygon([(350, 220), (400, 250), (350, 280), (300, 250)], fill='orange')
        
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        
        files = {
            'image': ('complex_test.jpg', img_buffer.getvalue(), 'image/jpeg')
        }
        
        data = {
            "question": "请详细分析这张图片，包括颜色、形状、文字等所有内容",
            "conversation_id": conversation_id,
            "stream": "false"
        }
        
        print(f"\n发送图片进行分析...")
        start_time = time.time()
        response = requests.post(url, headers=headers, data=data, files=files, timeout=120)
        end_time = time.time()
        
        print(f"响应时间: {end_time - start_time:.2f} 秒")
        print(f"响应状态码: {response.status_code}")
        print(f"响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('code') == 0 and result.get('data'):
                    data_content = result['data']
                    answer = data_content.get('answer', '')
                    
                    print(f"AI回答长度: {len(answer)} 字符")
                    
                    # 重点检查是否有循环重复
                    if len(answer) > 5000:
                        print("⚠️ 回答较长，检查重复...")
                        lines = answer.split('\n')
                        print(f"  总行数: {len(lines)}")
                        
                        # 检查重复行
                        line_counts = {}
                        for line in lines:
                            line = line.strip()
                            if line and len(line) > 10:  # 只检查有意义的行
                                line_counts[line] = line_counts.get(line, 0) + 1
                        
                        if line_counts:
                            max_repeat = max(line_counts.values())
                            if max_repeat > 5:
                                most_repeated = [line for line, count in line_counts.items() if count == max_repeat]
                                print(f"  🔄 最多重复行: '{most_repeated[0][:80]}...' 重复 {max_repeat} 次")
                                if max_repeat > 20:
                                    print("  ❌ 检测到严重的循环重复问题！")
                                else:
                                    print("  ⚠️ 有轻微重复，但可能正常")
                            else:
                                print("  ✅ 没有明显重复")
                    else:
                        print("✅ 回答长度正常")
                    
                    # 显示回答内容
                    if len(answer) > 1000:
                        print(f"回答内容预览: {answer[:500]}...")
                        print(f"回答内容结尾: ...{answer[-500:]}")
                    else:
                        print(f"完整回答: {answer}")
                        
                else:
                    print(f"API调用失败: {result}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print(f"API调用失败: {response.status_code} - {response.text}")
        
        return conversation_id
        
    except Exception as e:
        print(f"混合对话测试出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=== 开始真实场景的图片对话测试 ===")
    
    print("\n1. 测试纯文本对话")
    text_conv_id = test_text_only_chat()
    
    print("\n" + "="*60)
    print("2. 测试图片对话")
    image_conv_id = test_image_chat()
    
    print("\n" + "="*60)
    print("3. 测试混合对话")
    mixed_conv_id = test_mixed_conversation()
    
    print("\n" + "="*60)
    print("=== 测试完成 ===")
    print(f"文本对话ID: {text_conv_id}")
    print(f"图片对话ID: {image_conv_id}")
    print(f"混合对话ID: {mixed_conv_id}")
