#!/usr/bin/env python3
import sys
import os
import json
import requests
sys.path.append('/data/ragflow')

# 初始化设置
from api import settings
settings.init_settings()

from api.db.db_models import User, DB
from api.db.services.conversation_service import ConversationService

def test_conversation_content():
    """测试对话ID为440a067e714211f0ac23525400b50619的内容"""
    try:
        conversation_id = "440a067e714211f0ac23525400b50619"
        
        # 查询对话内容
        conversation = ConversationService.get_by_id(conversation_id)
        if conversation:
            print(f"对话ID: {conversation.id}")
            print(f"对话名称: {conversation.name}")
            print(f"用户ID: {conversation.user_id}")
            print(f"对话ID: {conversation.dialog_id}")
            print(f"创建时间: {conversation.create_time}")
            print(f"更新时间: {conversation.update_time}")
            
            # 打印消息内容
            if hasattr(conversation, 'message') and conversation.message:
                print(f"\n消息内容长度: {len(str(conversation.message))} 字符")
                print(f"消息类型: {type(conversation.message)}")
                
                # 如果是字符串，尝试解析为JSON
                if isinstance(conversation.message, str):
                    try:
                        message_data = json.loads(conversation.message)
                        print(f"解析后的消息数据类型: {type(message_data)}")
                        if isinstance(message_data, list):
                            print(f"消息数组长度: {len(message_data)}")
                            for i, msg in enumerate(message_data):
                                print(f"\n消息 {i+1}:")
                                print(f"  角色: {msg.get('role', 'unknown')}")
                                content = msg.get('content', '')
                                print(f"  内容长度: {len(str(content))} 字符")
                                if len(str(content)) > 1000:
                                    print(f"  内容预览: {str(content)[:500]}...")
                                    print(f"  内容结尾: ...{str(content)[-500:]}")
                                else:
                                    print(f"  内容: {content}")
                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                        print(f"原始消息内容长度: {len(conversation.message)}")
                        if len(conversation.message) > 1000:
                            print(f"消息内容预览: {conversation.message[:500]}...")
                            print(f"消息内容结尾: ...{conversation.message[-500:]}")
                        else:
                            print(f"消息内容: {conversation.message}")
                else:
                    print(f"消息内容: {conversation.message}")
            else:
                print("没有消息内容")
                
            return conversation
        else:
            print(f"未找到对话ID: {conversation_id}")
            return None
            
    except Exception as e:
        print(f"查询对话内容时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_call():
    """测试API调用图片对话接口"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        # 测试获取对话详情
        url = "http://localhost:9380/v1/conversation/get"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        params = {
            "conversation_id": "440a067e714211f0ac23525400b50619"
        }
        
        print(f"\n测试API调用: {url}")
        print(f"参数: {params}")
        
        response = requests.get(url, headers=headers, params=params)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据类型: {type(data)}")
                print(f"响应数据大小: {len(response.text)} 字符")
                
                if len(response.text) > 2000:
                    print(f"响应数据预览: {response.text[:1000]}...")
                    print(f"响应数据结尾: ...{response.text[-1000:]}")
                else:
                    print(f"响应数据: {data}")
                    
                return data
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"原始响应: {response.text[:1000]}...")
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"API调用出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== 测试数据库查询 ===")
    conversation = test_conversation_content()
    
    print("\n=== 测试API调用 ===")
    api_result = test_api_call()
