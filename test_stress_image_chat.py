#!/usr/bin/env python3
import json
import requests
import time
import os

def test_complex_image_analysis():
    """测试复杂图片分析，模拟可能触发长回答的场景"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        url = "http://localhost:9380/conversation/chat_with_vision"
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 创建一个复杂的测试图片
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # 创建一个更复杂的图片，包含更多元素
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制多种图形和文字
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'brown']
        
        # 绘制多个矩形
        for i in range(8):
            x = (i % 4) * 180 + 50
            y = (i // 4) * 120 + 50
            draw.rectangle([x, y, x+150, y+100], fill=colors[i], outline='black', width=2)
            draw.text((x+10, y+10), f"区域{i+1}", fill='white')
            draw.text((x+10, y+30), f"Area {i+1}", fill='white')
        
        # 添加更多复杂元素
        draw.ellipse([300, 300, 500, 450], fill='lightblue', outline='darkblue', width=3)
        draw.text((350, 360), "圆形区域", fill='black')
        draw.text((350, 380), "Circle Area", fill='black')
        
        # 绘制一些线条和多边形
        draw.line([(0, 300), (800, 300)], fill='black', width=3)
        draw.line([(400, 0), (400, 600)], fill='black', width=3)
        
        # 添加更多文字
        draw.text((50, 500), "这是一个复杂的测试图片，包含多种颜色、形状和文字", fill='black')
        draw.text((50, 520), "This is a complex test image with various colors, shapes and text", fill='black')
        draw.text((50, 540), "用于测试AI的图片理解能力和回答长度控制", fill='black')
        
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # 使用可能触发长回答的问题
        complex_questions = [
            "请详细分析这张图片的所有内容，包括每个区域的颜色、形状、文字，以及它们的位置关系",
            "请逐一描述图片中的每个元素，并分析它们的设计意图和可能的用途",
            "这张图片展示了什么？请从视觉设计、色彩搭配、布局结构等多个角度进行全面分析",
            "请详细解释图片中所有的文字内容，并分析图片的整体构图和设计理念"
        ]
        
        conversation_id = None
        
        for i, question in enumerate(complex_questions):
            print(f"\n=== 复杂问题 {i+1}: {question[:50]}... ===")
            
            files = {
                'image': ('complex_test.png', img_buffer.getvalue(), 'image/png')
            }
            
            data = {
                "question": question,
                "stream": "false"
            }
            
            if conversation_id:
                data["conversation_id"] = conversation_id
            
            start_time = time.time()
            response = requests.post(url, headers=headers, data=data, files=files, timeout=120)
            end_time = time.time()
            
            print(f"响应时间: {end_time - start_time:.2f} 秒")
            print(f"响应状态码: {response.status_code}")
            print(f"响应大小: {len(response.text)} 字符")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0 and result.get('data'):
                        data_content = result['data']
                        answer = data_content.get('answer', '')
                        conversation_id = data_content.get('conversation_id', conversation_id)
                        
                        print(f"AI回答长度: {len(answer)} 字符")
                        print(f"对话ID: {conversation_id}")
                        
                        # 重点检查是否有循环重复
                        if len(answer) > 3000:
                            print("⚠️ 回答较长，检查重复...")
                            lines = answer.split('\n')
                            print(f"  总行数: {len(lines)}")
                            
                            # 检查重复行
                            line_counts = {}
                            for line in lines:
                                line = line.strip()
                                if line and len(line) > 15:  # 只检查有意义的长行
                                    line_counts[line] = line_counts.get(line, 0) + 1
                            
                            if line_counts:
                                max_repeat = max(line_counts.values())
                                if max_repeat > 3:
                                    most_repeated = [line for line, count in line_counts.items() if count == max_repeat]
                                    print(f"  🔄 最多重复行: '{most_repeated[0][:60]}...' 重复 {max_repeat} 次")
                                    if max_repeat > 10:
                                        print("  ❌ 检测到循环重复问题！")
                                        return False
                                    else:
                                        print("  ⚠️ 有轻微重复，但在正常范围内")
                                else:
                                    print("  ✅ 没有明显重复")
                            
                            # 检查是否被截断（达到max_tokens限制）
                            if len(answer) > 1800:  # 接近2048的限制
                                print("  📏 回答可能被max_tokens限制截断，这是正常的")
                        else:
                            print("✅ 回答长度正常")
                        
                        # 显示回答内容片段
                        if len(answer) > 600:
                            print(f"回答开头: {answer[:300]}...")
                            print(f"回答结尾: ...{answer[-300:]}")
                        else:
                            print(f"完整回答: {answer}")
                            
                    else:
                        print(f"API调用失败: {result}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    return False
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                return False
                
            time.sleep(3)  # 避免请求过快
        
        print("\n✅ 复杂图片分析测试通过，没有发现循环重复问题")
        return True
        
    except Exception as e:
        print(f"复杂图片测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_long_conversation():
    """测试长对话，看看是否会在多轮对话中出现问题"""
    try:
        token = "17f5b4b06c5c11f08f56525400b50619"  # 罗莉的token
        
        url = "http://localhost:9380/conversation/chat_with_vision"
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        print(f"\n=== 长对话测试 ===")
        
        # 创建简单图片
        from PIL import Image, ImageDraw
        import io
        
        img = Image.new('RGB', (300, 200), color='lightgray')
        draw = ImageDraw.Draw(img)
        draw.text((50, 50), "长对话测试图片", fill='black')
        draw.text((50, 80), "Long conversation test", fill='blue')
        draw.rectangle([50, 120, 250, 180], outline='red', width=2)
        
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        
        # 进行多轮对话
        questions = [
            "请分析这张图片",
            "图片中的文字是什么意思？",
            "请详细描述图片的布局",
            "图片使用了哪些颜色？",
            "这张图片的设计有什么特点？",
            "请总结一下我们对这张图片的讨论",
            "你觉得这张图片还可以怎么改进？",
            "请再次详细分析这张图片的所有元素"
        ]
        
        conversation_id = None
        
        for i, question in enumerate(questions):
            print(f"\n--- 第 {i+1} 轮对话: {question} ---")
            
            files = {
                'image': ('long_test.jpg', img_buffer.getvalue(), 'image/jpeg')
            }
            
            data = {
                "question": question,
                "stream": "false"
            }
            
            if conversation_id:
                data["conversation_id"] = conversation_id
            
            response = requests.post(url, headers=headers, data=data, files=files, timeout=60)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0 and result.get('data'):
                        data_content = result['data']
                        answer = data_content.get('answer', '')
                        conversation_id = data_content.get('conversation_id', conversation_id)
                        
                        print(f"回答长度: {len(answer)} 字符")
                        
                        # 检查异常
                        if len(answer) > 4000:
                            print("⚠️ 回答过长")
                            return False
                        elif "ERROR" in answer:
                            print(f"⚠️ 回答包含错误: {answer}")
                            return False
                        else:
                            print("✅ 回答正常")
                        
                        # 显示简短回答
                        if len(answer) > 200:
                            print(f"回答: {answer[:100]}...{answer[-100:]}")
                        else:
                            print(f"回答: {answer}")
                            
                    else:
                        print(f"API调用失败: {result}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    return False
            else:
                print(f"API调用失败: {response.status_code}")
                return False
                
            time.sleep(1)
        
        print("\n✅ 长对话测试通过")
        return True
        
    except Exception as e:
        print(f"长对话测试出错: {e}")
        return False

if __name__ == "__main__":
    print("=== 开始压力测试 ===")
    
    success1 = test_complex_image_analysis()
    success2 = test_long_conversation()
    
    print("\n" + "="*60)
    print("=== 压力测试结果 ===")
    print(f"复杂图片分析测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"长对话测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！图片对话接口修复成功，没有发现循环重复问题。")
    else:
        print("\n⚠️ 部分测试失败，可能仍存在问题。")
